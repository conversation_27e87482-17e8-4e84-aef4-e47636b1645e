
<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        .table-container {
            width: 100%;
            overflow-x: auto;
            font-family: 'Inter', sans-serif;
        }

        .data-table-sector-weight {
            width: 100%;
            border-collapse: collapse;
            border: none;
        }

        .data-table-sector-weight th,
        .data-table-sector-weight td {
            padding: 8px;
            text-align: left;
            border: none;
        }

        .industry-header td {
            background: rgba(110, 154, 237, 0.1);
            font-weight: 600;
            padding: 12px 8px;
            color: #6E9AED;
        }

        .weight-cell {
            font-weight: 600;
            width: 200px;
            height: 40px;
        }
    </style>
</head>
<body>
    <div class="table-container">
        <table class="data-table-sector-weight">
            <thead>
                <tr>
                    <th>Symbol</th>
                    <th>Exchange</th>
                    <th>Value</th>
                    <th>Weight</th>
                </tr>
            </thead>
            <tbody id="tableBody">
            </tbody>
        </table>
    </div>

    <script>
        const data = [
            { symbol: 'RIO', exchange: 'asx', industry: 'Materials', value: 3939877 },
            { symbol: 'CBA', exchange: 'asx', industry: 'Banks', value: 2846931 },
            { symbol: 'NAB', exchange: 'asx', industry: 'Banks', value: 1329840 },
            { symbol: 'Cash', value: 3648 }
        ];

        function formatUnit(num) {
            if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
            if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
            if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
            return num.toFixed(2);
        }

        function createWeightBar(container, weight) {
            const chart = echarts.init(container);
            const option = {
                grid: { left: 0, right: 40, top: 0, bottom: 0 },
                xAxis: { type: 'value', show: false, max: 100 },
                yAxis: { type: 'category', show: false },
                series: [{
                    data: [weight],
                    type: 'bar',
                    barWidth: 20,
                    itemStyle: {
                        color: '#6E9AED',
                        borderRadius: [0, 2, 2, 0]
                    },
                    showBackground: true,
                    backgroundStyle: {
                        color: 'rgba(110, 154, 237, 0.1)',
                        borderRadius: 2
                    },
                    label: {
                        show: true,
                        position: 'right',
                        formatter: `${weight.toFixed(2)}%`,
                        color: '#6E9AED',
                        fontSize: 12,
                        fontWeight: 600
                    }
                }]
            };
            chart.setOption(option);
        }

        const tbody = document.getElementById('tableBody');
        const industries = [...new Set(data.map(item => item.industry).filter(Boolean))];
        const totalValue = data.reduce((sum, item) => sum + (item.value || 0), 0);

        industries.forEach(industry => {
            const industryHeader = document.createElement('tr');
            industryHeader.className = 'industry-header';
            industryHeader.innerHTML = `<td colspan="4">${industry}</td>`;
            tbody.appendChild(industryHeader);

            data.filter(item => item.industry === industry)
                .forEach(item => {
                    const weight = (item.value / totalValue * 100);
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${item.symbol}</td>
                        <td>${item.exchange || ''}</td>
                        <td>${formatUnit(item.value)}</td>
                        <td class="weight-cell"></td>
                    `;
                    tbody.appendChild(row);
                    createWeightBar(row.querySelector('.weight-cell'), weight);
                });
        });

        // Handle Other items
        const otherItems = data.filter(item => !item.industry);
        if (otherItems.length > 0) {
            const otherHeader = document.createElement('tr');
            otherHeader.className = 'industry-header';
            otherHeader.innerHTML = '<td colspan="4">Other</td>';
            tbody.appendChild(otherHeader);

            otherItems.forEach(item => {
                const weight = (item.value / totalValue * 100);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.symbol}</td>
                    <td>${item.exchange || ''}</td>
                    <td>${formatUnit(item.value)}</td>
                    <td class="weight-cell"></td>
                `;
                tbody.appendChild(row);
                createWeightBar(row.querySelector('.weight-cell'), weight);
            });
        }
    </script>
</body>
</html>
