
// HTML Section

<div class="table-container">
  <table class="data-table-sector-weight">
    <thead>
      <tr>
        <th>Symbol</th>
        <th>Exchange</th>
        <th>Value</th>
        <th>Weight</th>
      </tr>
    </thead>
    <tbody>
      {{#each data}}
      <tr>
        <td>{{symbol}}</td>
        <td>{{exchange}}</td>
        <td>{{formatUnit value}}</td>
      </tr>
      {{/each}}
    </tbody>
  </table>
</div>




// JavaScript Section before

function formatUnit(num) {
  if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
  if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
  if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
  return num.toFixed(2);
}

window.formatUnit = formatUnit;
context.handlebars.registerHelper('formatUnit', formatUnit);




// Javascript section after
console.log('Rendering table...');

const colorPalette = ['#5C85FF', '#B78AF7', '#FDEA6F', '#8EEE7C', '#FD946A', '#36DFF9', '#F48CC5', '#FE6A6A'];
let colorIndex = 0;

const tbody = document.querySelector('.data-table-sector-weight tbody');
const industries = [...new Set(context.data.map(item => item.industry).filter(Boolean))];
const totalValue = context.data.reduce((sum, item) => sum + (item.value || 0), 0);

// Clear and rebuild table
tbody.innerHTML = '';

// First render all HTML structure
industries.forEach(industry => {
    tbody.insertAdjacentHTML('beforeend', `
        <tr class="industry-header">
            <td colspan="4">${industry}</td>
        </tr>
    `);

    context.data
        .filter(item => item.industry === industry)
        .forEach(item => {
            const weight = (item.value / totalValue * 100).toFixed(2);
            const barColor = colorPalette[colorIndex % colorPalette.length]; // This will cycle through colors
            colorIndex++;
            tbody.insertAdjacentHTML('beforeend', `
              <tr>
                  <td>${item.symbol}</td>
                  <td>${item.exchange || ''}</td>
                  <td>${formatUnit(item.value)}</td>
                  <td class="weight-cell">
                      <div class="weight-bar" style="width: calc(${weight} * 10px); background: ${barColor}; --bar-color: ${barColor}"></div>
                      <div class="weight-tooltip">Value: ${formatUnit(item.value)} (${weight}%)</div>
                      ${weight}%
                  </td>
              </tr>
            `);
        });
});

// Add Other section if needed
const otherItems = context.data.filter(item => !item.industry);
if (otherItems.length > 0) {
    tbody.insertAdjacentHTML('beforeend', `
        <tr class="industry-header">
            <td colspan="4">Other</td>
        </tr>
    `);

    otherItems.forEach(item => {
        const weight = (item.value / totalValue * 100).toFixed(2);
        const barColor = colorPalette[colorIndex % colorPalette.length]; // This will cycle through colors
        colorIndex++;
        tbody.insertAdjacentHTML('beforeend', `
          <tr>
              <td>${item.symbol}</td>
              <td>${item.exchange || ''}</td>
              <td>${formatUnit(item.value)}</td>
              <td class="weight-cell">
                  <div class="weight-bar" style="width: calc(${weight} * 10px); background: ${barColor}; --bar-color: ${barColor}"></div>
                  <div class="weight-bar" style="width: calc(${weight}% * 0.8); background: ${barColor}; --bar-color: ${barColor}"></div>
                  <div class="weight-bar" style="width: min(${weight}%, calc(${weight}% * 0.8)); background: ${barColor}; --bar-color: ${barColor}"></div>
                  <div class="weight-bar" style="--weight-percent: ${weight}%; background: ${barColor}; --bar-color: ${barColor}"></div>

                  <div class="weight-tooltip">Value: ${formatUnit(item.value)} (${weight}%)</div>
                  ${weight}%
              </td>
          </tr>
        `);
    });
}

document.querySelectorAll('.weight-cell').forEach(cell => {
    cell.addEventListener('mousemove', (e) => {
        const tooltip = cell.querySelector('.weight-tooltip');
        const rect = cell.getBoundingClientRect();
        const cursorPositionInCell = e.clientX - rect.left;
        const cellMidpoint = rect.width / 2;

        if (cursorPositionInCell > cellMidpoint) {
            tooltip.style.left = `${e.clientX - rect.left - tooltip.offsetWidth - 10}px`;
        } else {
            tooltip.style.left = `${e.clientX - rect.left + 10}px`;
        }
        tooltip.style.top = `${e.clientY - rect.top - tooltip.offsetHeight / 2}px`;
    });
});




// Styles Section

.table-container {
  width: 100%;
  overflow-x: auto;
  font-family: 'Inter', sans-serif;
}

.dark-theme {
  background: #161622;
}

.light-theme{
  background: #ffffff;
}

.dark-theme th {
  background: #101018;
}

.light-theme th {
  background: rgba(0, 0, 0, 0.05);
}

.color-red { color: #EF476F; }
.color-yellow { color: #FFBC1F; }
.color-green { color: #06D6A0; }
.color-blue { color: #6E9AED; }

.color-high { color: #3ea32a; }
.color-low { color: #fd2121; }
.color-mid { color: #eab308; }

.dark-theme .color-high { color: #4fd036; }
.dark-theme .color-low { color: #fd4d4d; }
.dark-theme .color-mid { color: #eab308; }

.color-gray { color: #8C8C8C; }



.data-table-sector-weight {
  width: 100%;
  border-collapse: collapse;
  border: none;
}

.data-table-sector-weight th,
.data-table-sector-weight td {
  padding: 8px;
  text-align: left;
  border: none;
}

.data-table-sector-weight th {
  width: 20%; /* For equal widths */
}

/* OR specifically for weight column */
.data-table-sector-weight th:last-child {
  width: 200px;
}


// .light-theme .data-table-sector-weight tbody tr:nth-child(even) {
//   background: rgba(0, 0, 0, 0.05);
// }

// .dark-theme .data-table-sector-weight tbody tr:nth-child(even) {
//   background: #101018;
// }
  .weight-cell {
    position: relative;
    width: auto;
    display: flex;
    align-items: center;
    gap: 8px; /* Reduced from 16px for tighter layout */
    font-weight: 600;
  }

  /* Add responsive behavior for the weight bar */
  .weight-bar {
    background: var(--bar-color);
    height: 24px;
    border-radius: 3px;
    min-width: 1px;
    max-width: 150px; /* Add max-width control */
    transition: all 0.2s ease;
  }

  /* Add media queries for different widths */
  @media screen and (max-width: 768px) {
    .weight-bar {
      max-width: 100px;
    }
  }

  @media screen and (max-width: 576px) {
    .weight-bar {
      max-width: 50px;
    }
  }

  @media screen and (max-width: 400px) {
    .weight-bar {
      display: none;
    }
    .weight-cell {
      gap: 4px;
    }
  }
.weight-cell:hover .weight-bar {
  transform: scale(1.05);
  box-shadow: 0 0 6px 1px var(--bar-color);
}

.weight-cell:hover .weight-tooltip {
  display: block;
}

.weight-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: none;
  z-index: 10;
  pointer-events: none;
}


.weight-bar:hover + .weight-tooltip {
  display: block;
}


.data-table-sector-weight tbody tr {
  height: 40px; /* Ensures consistent row height */
}

.data-table-sector-weight th {
  position: sticky;
  top: 0;
  z-index: 1;
}

.industry-header td {
  background: rgba(110, 154, 237, 0.1);
  font-weight: 600;
  padding: 12px 8px;
  color: #6E9AED;
}

.dark-theme .industry-header td {
  background: rgba(110, 154, 237, 0.15);
}
