Van - <PERSON>h Dao Thu - An <PERSON><PERSON>en C gửi meeting memo:
Demo 25 / 12 / 2024
Dashboard:
Total Net Asset:  Vẽ line chart thể hiện chứ k để mỗi số
Mini chart Net Assets performance: có thêm đường line mini chart trên bar chart.
DB Widget về Portfolio: đang ptriển nốt
Top Account Value: Thêm cột Account Name, k chỉ để mỗi Account code
Heatmap:
Dữ liệu DB: sau sẽ phân biệt filter theo quý / month(Hiện tại: Đang theo Today và total)
Default là YTD, còn lại thì sẽ có option day to day
Nêncó nút export: PDF / Excel / Ảnh hoặc email > ấn để gửi thành email cho client
Gửi email: nhập email > Gửi từ box của harmonix / Equix(no reply @equix.com).Cho phép gửi email cho user trên Harmonix, hoặc theo những account mà advisors quản lý
Portfolio details:
Linked trading account: “View” thì nên là nút chứ k để hyper link
Create / Edit Portfolio: 
Adding thêm mũi tên Step 1 / 2 / 3
Đổi tên thành Portfolio Strategy
Chỉ nên có Allocation thôi chứ k nên có giá KH muốn fill > Giá phaỉ được cập nhật theo closed / last price. 
Set Portoflio strategy dựa vào tổng tiền định đầu tư thì mới quy ra đc số tiền và qty
Transaction Fee: k cho KH type theo % mà hiển thị dropdown(consider làm theo phí bậc thang)
Tickers: Format: ticker.exchange, và khi KH entered code vào thì ở dưới nên có tên công ty 
Đổi Cash > Cash Allocation
Note: Portfolio nên có tên(giới hạn ký tự) và short / long description(giới hạn ký tự)
Portfolio list:
Khi view list > có tooltip short description và view more > long description
Điền author của portfolio: Username / nickname > cho option ẩn hoặc hiện thông tin Author
Portoflio card:
a.Bỏ Expected return > replace bằng Portoflio P & L của 30 ngày và YTD
b.Last update details: ẩn đi > Replace bằng Risk Score: Đưa ra thang điểm tuỳ theo mức độ rủi ro(chia theo khối lượng sao: Càng nhiều sao rủi ro càng thấp > dùng công thức để tính sao : Tính dựa vào P & L details) >>> (Dùng theo efficience frontier: chỉ số Sharpe Ratio)
c.New Buy Point > Replace bằng số lượng Holding đang nắm giữ
Trading Account:
Historical adjustment: thêm thông tin người tạo / ng approve / thgian tạo
Rebalance Action:
Các cột
Thêm cột account id vào đầu
Symbol: Symbol.sàn + tên cổ phiếu
Volume:
Price: Default Market to Limit, allow client to enter Limit price
Thêm cột Destination: ASX / ASX BEST / TR > Allow client chọn Theo từng cổ phiếu 
Estimated Value: Dựa vào Market to Limit / Limit > tính ra value
Duration: Cho chọn trên header / Day / Good to Cancel / Good till date 
Thêm dòng 
total buy: tính tổng giá trị buy
Total sell: tính tổng giá trị sell
Freeeze
Panel ở đầu
Panel I hereby(disclaimer)
Add thêm:
Button Export: CSV 
Có màn hình Review / Confirm
2 options:
(1): chọn dialog to dialog
  (2): Extend thêm màn review rồi ấn confirm > Designer consider option nào hợp lý
Thêm nút Back ở màn review
  (Phase 2) Efficient Frontier(Portfolio Optimization)
Sharpe Ratio: Đổi thành sao thay vì để số cụ thể  kèm thêm tool tip: Tối đa 3 sao