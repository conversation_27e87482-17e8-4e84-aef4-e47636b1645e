//SECTION FOR ECHARTS BUSINESS TEXT WITH CHART

// query set 0
select * from public.get_account_pnl_summarize('${author_id}', ${__from}, ${__to}, '${__timezone}') ORDER BY updated_at ASC

// content section
<div id="panel-container">
  <!-- This div will be managed by ECharts -->
</div>

// JS before section
// No additional JS needed before the main code

// JS after section
// Determine the current theme
const isDarkTheme = context.grafana.theme.isDark === true;

// Define colors for light and dark themes
const lightThemeColors = ['#eab308', '#3ea32a', '#fd2121', '#6200EE', '#B78AF7'];
const darkThemeColors = ['#eab308', '#4fd036', '#fd4d4d', '#6200EE', '#B78AF7'];

// Select the appropriate color set based on the theme
const themeColors = isDarkTheme ? darkThemeColors : lightThemeColors;

// Format number helper function
const formatNumber = (num) => {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(2) + 'B';
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(2) + 'M';
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(2) + 'K';
  } else {
    return num.toFixed(2);
  }
};

// Get the cash value and percentage from the data
const cashValue = parseFloat(context.data[0].cash);
const cashPercentage = parseFloat(context.data[0].cash_percentage);

// Determine the trend indicator
const getTrendPrefix = (value) => {
  if (value > 0) return '▲';
  if (value < 0) return '▼';
  return '●';
};

// Determine the trend color
const getTrendColor = (value) => {
  if (value > 0) return themeColors[1]; // positive
  if (value < 0) return themeColors[2]; // negative
  return themeColors[0]; // neutral
};

// Get the chart container element
const container = document.getElementById('panel-container');

// Create a chart instance
const chart = echarts.init(container);

// Get data for the chart
const cashData = context.data.map(item => ({
  value: parseFloat(item.cash),
  time: new Date(item.updated_at).getTime()
})).sort((a, b) => a.time - b.time);

// Extract values and times for the chart
const values = cashData.map(item => item.value);
const times = cashData.map(item => item.time);

// Configure chart options
const option = {
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'axis',
    formatter: function(params) {
      const date = new Date(params[0].value[0]);
      const formattedDate = date.toLocaleDateString();
      const value = params[0].value[1];
      return `${formattedDate}<br/>${value.toLocaleString()} AUD`;
    }
  },
  grid: {
    left: '60%',  // Move the chart to the right side
    right: '5%',
    top: '10%',   // Reduce top margin to make chart taller
    bottom: '10%',
    width: '35%', // Make the chart width smaller
    containLabel: true
  },
  xAxis: {
    type: 'time',
    show: true,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      formatter: function(value) {
        const date = new Date(value);
        return date.toLocaleDateString();
      },
      color: isDarkTheme ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
    },
    splitLine: { show: false }
  },
  yAxis: {
    type: 'value',
    show: true,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      formatter: function(value) {
        return formatNumber(value);
      },
      color: isDarkTheme ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
    },
    splitLine: {
      lineStyle: {
        color: isDarkTheme ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
      }
    }
  },
  graphic: {
    elements: [
      {
        // Background rectangle for the text area
        type: 'rect',
        left: '5%',
        top: 10,
        z: 100,
        shape: { width: '40%', height: '100%', r: 10 },
        style: {
          fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
          stroke: isDarkTheme ? '#333' : '#ccc',
          lineWidth: 0,
        }
      },
      {
        // Text element for displaying the cash value and percentage
        type: 'text',
        left: '10%',
        top: '40%',
        z: 101,
        style: {
          text: `{title|Cash Balance}\n{cash|$${formatNumber(cashValue)} AUD} {percentage|${getTrendPrefix(cashPercentage)} ${cashPercentage.toFixed(2)}%}`,
          textAlign: 'left',
          rich: {
            title: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#888888',
              padding: [0, 0, 20, 0]
            },
            cash: {
              fontSize: 28,
              fontWeight: 'bold',
              lineHeight: 36,
              color: isDarkTheme ? 'white' : 'black',
            },
            percentage: {
              fontSize: 18,
              padding: [0, 0, 0, 10],
              color: getTrendColor(cashPercentage)
            }
          }
        }
      }
    ]
  },
  series: [{
    data: values.map((value, index) => [times[index], value]),
    type: 'line',
    smooth: true,
    symbol: 'circle',
    symbolSize: 6,
    showSymbol: false,
    lineStyle: {
      width: 3,
      color: themeColors[3]
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: themeColors[3]
        }, {
          offset: 1,
          color: 'rgba(98, 0, 238, 0.1)'
        }]
      }
    }
  }],
  media: [
    {
      // For larger screens (>=690px)
      query: { minWidth: 690 },
      option: {
        grid: {
          left: '60%',  // Move the chart to the right side
          right: '5%',
          top: '10%',   // Reduce top margin to make chart taller
          bottom: '10%',
          width: '35%', // Make the chart width smaller
        },
        graphic: {
          elements: [
            {
              // Background rectangle for the text area
              type: 'rect',
              left: '5%',
              top: 10,
              z: 100,
              shape: { width: '40%', height: '100%', r: 10 },
              style: {
                fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
                stroke: isDarkTheme ? '#333' : '#ccc',
                lineWidth: 0,
              }
            },
            {
              // Text element for displaying the cash value and percentage
              type: 'text',
              left: '10%',
              top: '40%',
              z: 101,
              style: {
                text: `{title|Cash Balance}\n{cash|$${formatNumber(cashValue)} AUD} {percentage|${getTrendPrefix(cashPercentage)} ${cashPercentage.toFixed(2)}%}`,
                textAlign: 'left',
                rich: {
                  title: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#888888',
                    padding: [0, 0, 20, 0]
                  },
                  cash: {
                    fontSize: 32,
                    fontWeight: 'bold',
                    lineHeight: 40,
                    color: isDarkTheme ? 'white' : 'black',
                  },
                  percentage: {
                    fontSize: 20,
                    padding: [0, 0, 0, 10],
                    color: getTrendColor(cashPercentage)
                  }
                }
              }
            }
          ]
        }
      }
    },
    {
      // For smaller screens (<690px)
      query: { maxWidth: 691 },
      option: {
        grid: {
          left: '0',
          right: '0',
          top: '50%',
          bottom: '10%',
          width: '100%',
        },
        graphic: {
          elements: [
            {
              // Background rectangle for the text area
              type: 'rect',
              left: 'center',
              top: 10,
              z: 100,
              shape: { width: '90%', height: '40%', r: 10 },
              style: {
                fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
                stroke: isDarkTheme ? '#333' : '#ccc',
                lineWidth: 0,
              }
            },
            {
              // Text element for displaying the cash value and percentage
              type: 'text',
              left: 'center',
              top: '10%',
              z: 101,
              style: {
                text: `{title|Cash Balance}\n{cash|$${formatNumber(cashValue)} AUD} {percentage|${getTrendPrefix(cashPercentage)} ${cashPercentage.toFixed(2)}%}`,
                textAlign: 'center',
                rich: {
                  title: {
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#888888',
                    padding: [0, 0, 10, 0]
                  },
                  cash: {
                    fontSize: 24,
                    fontWeight: 'bold',
                    lineHeight: 30,
                    color: isDarkTheme ? 'white' : 'black',
                  },
                  percentage: {
                    fontSize: 16,
                    padding: [0, 0, 0, 10],
                    color: getTrendColor(cashPercentage)
                  }
                }
              }
            }
          ]
        }
      }
    }
  ]
};

// Set chart options
chart.setOption(option);

// Handle window resize
window.addEventListener('resize', function() {
  chart.resize();
});

// styles
/* No additional CSS needed as everything is handled by ECharts */

// END OF SECTION ECHARTS BUSINESS TEXT WITH CHART
