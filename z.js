// Check if data exists
const hasData = context.panel.data.series[0].fields.length > 0;

if (!hasData) {
  // Return a configuration that displays a custom error message
  return {
    title: {
      text: 'No data available',
      left: 'center',
      top: 'center',
      textStyle: {
        color: 'grey',
        fontSize: 20,
      },
    },
    backgroundColor: 'transparent',
  };
}

// Determine the current theme
const isDarkTheme = context.grafana.theme.isDark === true;
// Define colors for light and dark themes
const lightThemeColors = ['#eab308', '#3ea32a', '#fd2121', '#FF7700', '#FF871F'];
const darkThemeColors = ['#eab308', '#4fd036', '#fd4d4d', '#FF7700', '#FF871F'];
// Select the appropriate color set based on the theme
const themeColors = isDarkTheme ? darkThemeColors : lightThemeColors;

const formatNumber = (num) => {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(2) + 'B';
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(2) + 'M';
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(2) + 'K';
  } else {
    return num.toFixed(2);
  }
};

// Custom function to round up to the nearest integer
const roundUp = (num) => Math.ceil(num);

const sCount = context.panel.data.series.flatMap((s) => s.fields.find((f) => f.name === 'count').values);
const totalCount = sCount.reduce((sum, current) => sum + current, 0);
const sTime = context.panel.data.series.flatMap((s) => s.fields.find((f) => f.name === 'to_char').values);

const dataCount = sCount.map((c, i) => ({
  value: sCount.slice(0, i + 1).reduce((sum, current) => sum + current, 0),
  time: sTime[i],
}));

// Calculate the minimum and maximum values of the data
const minValue = Math.min(sCount);
const maxValue = Math.max(sCount);

// Extend the y-axis range by 10% both above the maximum value and below the minimum value
const yAxisMin = minValue > 0 ? roundUp(minValue * 0.9) : roundUp(minValue * 1.1);
const yAxisMax = roundUp(maxValue * 1.1);

const series = [
  {
    name: 'User by month',
    type: 'line',
    showSymbol: false,
    areaStyle: {
      opacity: 0.2,  // Slightly more transparent
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: themeColors[3]
        }, {
          offset: 1,
          color: isDarkTheme ? 'rgba(0,0,0,0)' : 'rgba(255,255,255,0)'  // Fade to transparent
        }]
      }
    },
    lineStyle: {
      width: 1.5,  // Thinner line for mini chart
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: themeColors[3]
        }, {
          offset: 1,
          color: themeColors[4]
        }]
      }
    },
    smooth: true,
    data: dataCount,
    silent: true,
    cursor: 'default',
    emphasis: {
      disabled: true
    },
    tooltip: {
      show: false
    },
    animation: false  // Disable animation for better performance in small chart
  },
]

/**
 * Enable Data Zoom by default
 */
setTimeout(() => context.panel.chart.dispatchAction({
  type: 'takeGlobalCursor',
  key: 'dataZoomSelect',
  dataZoomSelectActive: true,
}), 500);

/**
 * Update Time Range on Zoom
 */
context.panel.chart.on('datazoom', function (params) {
  const startValue = params.batch[0]?.startValue;
  const endValue = params.batch[0]?.endValue;
  locationService.partial({ from: startValue, to: endValue });
});

return {
  backgroundColor: 'transparent',
  tooltip: {
    // trigger: 'axis',
  },
  legend: {
    show: false,
    left: '0',
    bottom: '0',
    data: context.panel.data.series.map((s) => s.refId),
    textStyle: {
      color: 'rgba(128, 128, 128, .9)',
    },
  },
  xAxis: {
    type: 'category',
    show: false,
    boundaryGap: false,  // Start the line from the edge of the chart
  },
  yAxis: {
    show: false,
    type: 'value',
    min: yAxisMin,
    max: yAxisMax,
    scale: true,  // Better scale for mini chart
  },
  grid: {
    left: '60%',  // Move the chart to the right side
    right: '5%',
    top: '10%',   // Reduce top margin to make chart taller
    bottom: '10%',
    width: '35%', // Make the chart width smaller
    containLabel: false
  },
  graphic: {
    elements: [
      {
        type: 'rect',
        left: '5%',
        top: 10,
        z: 100,
        shape: {
          width: '40%',  // Reduce rectangle width to 40% to keep it more to the left
          height: '100%',
          r: 10
        },
        style: {
          fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
          stroke: isDarkTheme ? '#333' : '#ccc',
          lineWidth: 0,
          cursor: 'default'
        }
      },
      {
        type: 'text',
        left: '5%',  // Position at 25% from left (center of the 40% width rectangle)
        top: '45%',   // Center vertically
        z: 101,
        style: {
          cursor: 'text',  // Sets text selection cursor
          userSelect: 'text',  // Enables text selection
          pointerEvents: 'auto',  // Ensures text interactions are enabled
          position: 'center',  // Center the text at the position point
          textAlign: 'center', // Center-align the text content
          textVerticalAlign: 'middle', // Vertically center the text
          text: `{user|${totalCount}} {newU|${(() => {
            const newU = sCount[sCount.length - 1];
            const prefix = newU > 0 ? '▲' : (newU < 0 ? '▼' : '●');
            return `${prefix} ${newU}`;
          })()}}`,
          rich: {
            user: {
              fontSize: 40,
              fontWeight: 'bold',
              fill: isDarkTheme ? 'white' : 'black',
            },
            newU: {
              fontSize: 20,
              padding: [7, 0, 0, 10],
              fill: (() => {
                const newU = sCount[sCount.length - 1];
                return newU > 0 ? themeColors[1] : (newU < 0 ? themeColors[2] : themeColors[0]);
              })()
            }
          }
        }
      }
    ]
  },
  media: [
    {
      // For larger screens (>=690px)
      query: { minWidth: 690 },
      option: {
        grid: {
          left: '60%',  // Move the chart to the right side
          right: '5%',
          top: '30%',   // Reduce top margin to make chart taller
          bottom: '30%',
          width: '35%', // Make the chart width smaller
        },
        graphic: {
          elements: [
            {
              // Rectangle config for larger screens
              type: 'rect',
              left: '5%',
              top: 10,
              z: 100,
              shape: { width: '40%', height: '100%', r: 10 },
              style: {
                fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
                stroke: isDarkTheme ? '#333' : '#ccc',
                lineWidth: 0,
              }
            },
            {
              type: 'text',
              left: '5%',  // Position at 25% from left (center of the 40% width rectangle)
              top: '45%',   // Center vertically
              z: 101,
              style: {
                cursor: 'text',  // Sets text selection cursor
                userSelect: 'text',  // Enables text selection
                pointerEvents: 'auto',  // Ensures text interactions are enabled
                position: 'center',  // Center the text at the position point
                textAlign: 'center', // Center-align the text content
                textVerticalAlign: 'middle', // Vertically center the text
                text: `{user|${totalCount}} {newU|${(() => {
                  const newU = sCount[sCount.length - 1];
                  const prefix = newU > 0 ? '▲' : (newU < 0 ? '▼' : '●');
                  return `${prefix} ${newU}`;
                })()}}`,
                rich: {
                  user: {
                    fontSize: 60,
                    fontWeight: 'bold',
                    fill: isDarkTheme ? 'white' : 'black',
                  },
                  newU: {
                    fontSize: 30,
                    padding: [7, 0, 0, 10],
                    fill: (() => {
                      const newU = sCount[sCount.length - 1];
                      return newU > 0 ? themeColors[1] : (newU < 0 ? themeColors[2] : themeColors[0]);
                    })()
                  }
                }
              }
            }
          ]
        }
      }
    },
    {
      // For smaller screens (<690px)
      query: { maxWidth: 691 },
      option: {
        grid: {
          left: '60%',  // Move the chart to the right side
          right: '5%',
          top: '10%',   // Reduce top margin to make chart taller
          bottom: '10%',
          width: '35%', // Make the chart width smaller
        },
        graphic: {
          elements: [
            {
              // Rectangle config for smaller screens
              type: 'rect',
              left: '5%',
              top: 10,
              z: 100,
              shape: { width: '40%', height: '100%', r: 10 },
              style: {
                fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
                stroke: isDarkTheme ? '#333' : '#ccc',
                lineWidth: 0,
              }
            },
            {
              type: 'text',
              left: '5%',  // Position at 25% from left (center of the 40% width rectangle)
              top: '45%',   // Center vertically
              z: 101,
              style: {
                cursor: 'text',  // Sets text selection cursor
                userSelect: 'text',  // Enables text selection
                pointerEvents: 'auto',  // Ensures text interactions are enabled
                position: 'center',  // Center the text at the position point
                textAlign: 'center', // Center-align the text content
                textVerticalAlign: 'middle', // Vertically center the text
                text: `{user|${totalCount}} {newU|${(() => {
                  const newU = sCount[sCount.length - 1];
                  const prefix = newU > 0 ? '▲' : (newU < 0 ? '▼' : '●');
                  return `${prefix} ${newU}`;
                })()}}`,
                rich: {
                  user: {
                    fontSize: 40,
                    fontWeight: 'bold',
                    fill: isDarkTheme ? 'white' : 'black',
                  },
                  newU: {
                    fontSize: 20,
                    padding: [7, 0, 0, 10],
                    fill: (() => {
                      const newU = sCount[sCount.length - 1];
                      return newU > 0 ? themeColors[1] : (newU < 0 ? themeColors[2] : themeColors[0]);
                    })()
                  }
                }
              }
            }
          ]
        }
      }
    }
  ],
  series,
};


