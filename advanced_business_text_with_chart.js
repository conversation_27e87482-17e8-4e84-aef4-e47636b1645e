//SECTION FOR ADVANCED BUSINESS TEXT WITH CHART

// query set 0
select * from public.get_account_pnl_summarize('${author_id}', ${__from}, ${__to}, '${__timezone}') ORDER BY updated_at ASC

// content section
<div class="panel-container">
  <div class="content-wrapper">
    <div class="left-content">
      <div class="title">Cash Balance</div>
      <div class="net-diff-container">
        <div class="net-value">
          {{data.0.cash}} AUD
        </div>
        <div class="diff-value"
          data-trend="{{#if (and (gt data.0.cash_percentage -0.0001) (lt data.0.cash_percentage 0.0001))}}neutral{{else if (gt data.0.cash_percentage 0)}}positive{{else if (lt data.0.cash_percentage 0)}}negative{{else}}neutral{{/if}}">
          <span class="trend-indicator"></span>
          <span class="trend-value">{{toFixed data.0.cash_percentage 2}}%</span>
        </div>
      </div>
      <div class="additional-info">
        <div class="info-row">
          <span class="info-label">Last Updated:</span>
          <span class="info-value">{{formatDate data.0.updated_at "MMM DD, YYYY"}}</span>
        </div>
        <div class="info-row">
          <span class="info-label">Account:</span>
          <span class="info-value">{{data.0.account_name}}</span>
        </div>
      </div>
    </div>
    <div class="right-content" id="chart-container">
      <!-- Chart will be rendered here by JavaScript -->
    </div>
  </div>
</div>

// JS before section
// Format date helper function
function formatDate(dateStr, format) {
  const date = new Date(dateStr);
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  // Replace tokens in format string
  return format
    .replace('YYYY', date.getFullYear())
    .replace('MMM', months[date.getMonth()])
    .replace('DD', date.getDate().toString().padStart(2, '0'));
}

// Register the helper with Handlebars
Handlebars.registerHelper('formatDate', formatDate);

// We need to create a chart when the panel is rendered
function setupChart() {
  // Find the chart container element
  const chartContainer = document.getElementById('chart-container');
  if (!chartContainer) return;
  
  // Make sure we have the ECharts library
  if (typeof echarts === 'undefined') {
    console.error('ECharts library not found');
    return;
  }
  
  // Create a chart instance
  const chart = echarts.init(chartContainer);
  
  // Get data from the panel context
  const cashData = context.data.map(item => ({
    value: parseFloat(item.cash),
    time: new Date(item.updated_at).getTime()
  })).sort((a, b) => a.time - b.time);
  
  // Extract values and times for the chart
  const values = cashData.map(item => item.value);
  const times = cashData.map(item => item.time);
  
  // Determine theme colors
  const isDarkTheme = context.grafana.theme.isDark === true;
  const lineColor = isDarkTheme ? '#4fd036' : '#3ea32a';
  const areaColor = isDarkTheme ? 'rgba(79, 208, 54, 0.2)' : 'rgba(62, 163, 42, 0.2)';
  
  // Configure chart options
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '5%',
      top: '15%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        formatter: function(value) {
          const date = new Date(value);
          return date.toLocaleDateString();
        },
        color: isDarkTheme ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
      },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        formatter: function(value) {
          if (value >= 1e9) return (value / 1e9).toFixed(1) + 'B';
          if (value >= 1e6) return (value / 1e6).toFixed(1) + 'M';
          if (value >= 1e3) return (value / 1e3).toFixed(1) + 'K';
          return value.toFixed(0);
        },
        color: isDarkTheme ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
      },
      splitLine: {
        lineStyle: {
          color: isDarkTheme ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const date = new Date(params[0].value[0]);
        const formattedDate = date.toLocaleDateString();
        const value = params[0].value[1];
        return `${formattedDate}<br/>${value.toLocaleString()} AUD`;
      }
    },
    series: [{
      data: values.map((value, index) => [times[index], value]),
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      showSymbol: false,
      lineStyle: {
        width: 3,
        color: lineColor
      },
      areaStyle: {
        color: areaColor
      }
    }]
  };
  
  // Set chart options
  chart.setOption(option);
  
  // Store chart instance for later access
  window.cashBalanceChart = chart;
  
  // Handle window resize
  window.addEventListener('resize', function() {
    if (window.cashBalanceChart) {
      window.cashBalanceChart.resize();
    }
  });
}

// JS after section
// Initialize chart after the panel is rendered
setTimeout(setupChart, 100);

// styles
.panel-container {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 15px;
  position: relative;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.content-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
}

.left-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  text-align: left;
  width: 40%;
  padding: 20px;
  position: relative;
  z-index: 2;
}

.right-content {
  width: 60%;
  height: 100%;
  min-height: 200px;
  position: relative;
  z-index: 1;
}

.title {
  font-size: 1.2em;
  font-weight: 500;
  color: #888888;
  margin-bottom: 15px;
  text-align: left;
}

.net-diff-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 20px;
}

.net-value {
  font-size: 2.5em;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 10px;
  text-align: left;
}

.diff-value {
  font-size: 1.2em;
  line-height: 1;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 5px;
}

.additional-info {
  margin-top: 20px;
  width: 100%;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9em;
}

.info-label {
  color: #888888;
}

.info-value {
  font-weight: 500;
}

/* Style based on trend data attribute */
.diff-value[data-trend="positive"] {
  color: #79EA62;
}

.diff-value[data-trend="negative"] {
  color: #FD4D4D;
}

.diff-value[data-trend="neutral"] {
  color: #888888;
}

/* Trend indicators */
.trend-indicator {
  display: inline-block;
  margin-right: 4px;
}

.diff-value[data-trend="positive"] .trend-indicator::before {
  content: "▲";
}

.diff-value[data-trend="negative"] .trend-indicator::before {
  content: "▼";
}

.diff-value[data-trend="neutral"] .trend-indicator::before {
  content: "●";
}

/* Media queries for responsive layout */
@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }
  
  .left-content, .right-content {
    width: 100%;
  }
  
  .left-content {
    padding: 15px;
    margin-bottom: 0;
  }
  
  .right-content {
    min-height: 200px;
  }
}

// END OF SECTION ADVANCED BUSINESS TEXT WITH CHART
