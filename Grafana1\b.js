//SECTION FOR BUSINESS TEXT WITH CHART

// query set 0
select * from public.get_account_pnl_summarize('${author_id}', ${__from}, ${__to}, '${__timezone}') ORDER BY updated_at ASC

// content section
<div class="panel-container">
  <div class="content-wrapper">
    <div class="left-content">
      <div class="title">Cash Balance</div>
      <div class="net-diff-container">
        <div class="net-value">
          {{data.0.cash}} AUD
        </div>
        <div class="diff-value"
          data-trend="{{#if (and (gt data.0.cash_percentage -0.0001) (lt data.0.cash_percentage 0.0001))}}neutral{{else if (gt data.0.cash_percentage 0)}}positive{{else if (lt data.0.cash_percentage 0)}}negative{{else}}neutral{{/if}}">
          <span class="trend-indicator"></span>
          <span class="trend-value">{{toFixed data.0.cash_percentage 2}}%</span>
        </div>
      </div>
    </div>
    <div class="right-content" id="chart-container">
      <!-- Chart will be rendered here by JavaScript -->
    </div>
  </div>
</div>

// JS before section
// We need to create a chart when the panel is rendered
// This function will be called after the panel is rendered
function setupChart() {
  // Find the chart container element
  const chartContainer = document.getElementById('chart-container');
  if (!chartContainer) return;

  // Make sure we have the ECharts library
  if (typeof echarts === 'undefined') {
    console.error('ECharts library not found');
    return;
  }

  // Create a chart instance
  const chart = echarts.init(chartContainer);

  // Get data from the panel context
  const cashData = context.data.map(item => ({
    value: parseFloat(item.cash),
    time: new Date(item.updated_at).getTime()
  })).sort((a, b) => a.time - b.time);

  // Extract values and times for the chart
  const values = cashData.map(item => item.value);
  const times = cashData.map(item => item.time);

  // Determine theme colors
  const isDarkTheme = context.grafana.theme.isDark === true;
  const lineColor = isDarkTheme ? '#4fd036' : '#3ea32a';
  const areaColor = isDarkTheme ? 'rgba(79, 208, 54, 0.2)' : 'rgba(62, 163, 42, 0.2)';

  // Configure chart options
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '5%',
      top: '15%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        formatter: function(value) {
          const date = new Date(value);
          return date.toLocaleDateString();
        },
        color: isDarkTheme ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
      },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        formatter: function(value) {
          if (value >= 1e9) return (value / 1e9).toFixed(1) + 'B';
          if (value >= 1e6) return (value / 1e6).toFixed(1) + 'M';
          if (value >= 1e3) return (value / 1e3).toFixed(1) + 'K';
          return value.toFixed(0);
        },
        color: isDarkTheme ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
      },
      splitLine: {
        lineStyle: {
          color: isDarkTheme ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    series: [{
      data: values.map((value, index) => [times[index], value]),
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: {
        width: 3,
        color: lineColor
      },
      areaStyle: {
        color: areaColor
      }
    }]
  };

  // Set chart options
  chart.setOption(option);

  // Store chart instance for later access
  window.cashBalanceChart = chart;

  // Handle window resize
  window.addEventListener('resize', function() {
    if (window.cashBalanceChart) {
      window.cashBalanceChart.resize();
    }
  });
}

// JS after section
// Initialize chart after the panel is rendered
setTimeout(setupChart, 100);

// styles
.panel-container {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 10px;
  position: relative;
}

.content-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
}

.left-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  width: 40%;
  padding-right: 20px;
}

.right-content {
  width: 60%;
  height: 100%;
  min-height: 150px;
}

.title {
  font-size: 1.5em;
  font-weight: bold;
  color: #888888;
  margin-bottom: 15px;
  text-align: left;
}

.net-diff-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
}

.net-value {
  font-size: 2.5em;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 10px;
  text-align: left;
}

.diff-value {
  font-size: 1.5em;
  line-height: 1;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Style based on trend data attribute */
.diff-value[data-trend="positive"] {
  color: #79EA62;
}

.diff-value[data-trend="negative"] {
  color: #FD4D4D;
}

.diff-value[data-trend="neutral"] {
  color: #888888;
}

/* Trend indicators */
.trend-indicator {
  display: inline-block;
  margin-right: 4px;
}

.diff-value[data-trend="positive"] .trend-indicator::before {
  content: "▲";
}

.diff-value[data-trend="negative"] .trend-indicator::before {
  content: "▼";
}

.diff-value[data-trend="neutral"] .trend-indicator::before {
  content: "●";
}

/* Media queries for responsive layout */
@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }

  .left-content, .right-content {
    width: 100%;
  }

  .left-content {
    margin-bottom: 20px;
    padding-right: 0;
  }

  .right-content {
    min-height: 200px;
  }
}

// END OF SECTION BUSINESS TEXT






// SECTION OF BUSINESS CHART (line chart)

// query for 1

select * from public.get_account_pnl_summarize('${author_id}', ${__from}, ${__to}) ORDER BY updated_at ASC


// section code of this plugin

// Determine the current theme
const isDarkTheme = context.grafana.theme.isDark === true;
// Define colors for light and dark themes
const lightThemeColors = ['#eab308', '#3ea32a', '#fd2121', '#6200EE', '#B78AF7'];
const darkThemeColors = ['#eab308', '#4fd036', '#fd4d4d', '#6200EE', '#B78AF7'];
// Select the appropriate color set based on the theme
const themeColors = isDarkTheme ? darkThemeColors : lightThemeColors;

const formatNumber = (num) => {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(2) + 'B';
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(2) + 'M';
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(2) + 'K';
  } else {
    return num.toFixed(2);
  }
};

// Custom function to round up to the nearest integer
const roundUp = (num) => Math.ceil(num);

// Calculate the minimum and maximum values of the data
const minValue = Math.min(...context.panel.data.series.flatMap(s => s.fields.find(f => f.name === 'net_asset').values));
const maxValue = Math.max(...context.panel.data.series.flatMap(s => s.fields.find(f => f.name === 'net_asset').values));

// Extend the y-axis range by 10% both above the maximum value and below the minimum value
const yAxisMin = minValue > 0 ? roundUp(minValue * 0.9) : roundUp(minValue * 1.1);
const yAxisMax = roundUp(maxValue * 1.1);

const sNet = context.panel.data.series.flatMap((s) => s.fields.find((f) => f.name === 'net_asset').values).reverse();
const sPnl = context.panel.data.series.flatMap((s) => s.fields.find((f) => f.name === 'today_account_pnl').values).reverse();

const series = context.panel.data.series.map((s, index) => {
  const sData = s.fields.find((f) => f.type === 'number').values.buffer || s.fields.find((f) => f.type === 'number').values;
  const sTime = s.fields.find((f) => f.type === 'time').values.buffer || s.fields.find((f) => f.type === 'time').values;

  return {
    name: s.refId,
    type: 'line',
    showSymbol: false,
    areaStyle: {
      opacity: 0.3,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: themeColors[3]
        }, {
          offset: 1,
          color: '#ffffff'
        }]
      }
    },
    lineStyle: {
      width: 2,
      // color: themeColors[3],
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: themeColors[3]
        }, {
          offset: 1,
          color: themeColors[4]
        }]
      }
    },
    smooth: true,
    data: sData.map((d, i) => [sTime[i], d.toFixed(2)]),
    silent: true,
    cursor: 'default',
    emphasis: {
      disabled: true
    },
  };
});

graphic: {
  elements: [
    {
      type: 'rect',
      silent: true,
      style: {
        cursor: 'default',
      }
    },
    {
      type: 'text',
      style: {
        cursor: 'text',  // Sets text selection cursor
        userSelect: 'text',  // Enables text selection
        pointerEvents: 'auto',  // Ensures text interactions are enabled
        textStyle: {
          cursor: 'text',  // Reinforces text cursor on the actual text content
        }
      }
    }
  ]
}

/**
 * Enable Data Zoom by default
 */
setTimeout(() => context.panel.chart.dispatchAction({
  type: 'takeGlobalCursor',
  key: 'dataZoomSelect',
  dataZoomSelectActive: true,
}), 500);

/**
 * Update Time Range on Zoom
 */
context.panel.chart.on('datazoom', function (params) {
  const startValue = params.batch[0]?.startValue;
  const endValue = params.batch[0]?.endValue;
  locationService.partial({ from: startValue, to: endValue });
});

return {
  backgroundColor: 'transparent',
  tooltip: {
    // trigger: 'axis',
  },
  legend: {
    show: false,
    left: '0',
    bottom: '0',
    data: context.panel.data.series.map((s) => s.refId),
    textStyle: {
      color: 'rgba(128, 128, 128, .9)',
    },
  },
  // toolbox: {
  //   feature: {
  //     dataZoom: {
  //       yAxisIndex: 'none',
  //       icon: {
  //         zoom: 'path://',
  //         back: 'path://',
  //       },
  //     },
  //     saveAsImage: {},
  //   }
  // },
  xAxis: {
    type: 'time',
    show: false,
  },
  yAxis: {
    show: false,
    type: 'value',
    min: 'dataMin',
    max: yAxisMax,
  },
  grid: {
    left: '0',
    right: '0',
    top: '35%',
    bottom: 0,
    containLabel: false
  },
  graphic: {
    elements: [
      {
        type: 'rect',
        left: 'center',
        top: 10,
        z: 100,
        shape: {
          width: '100%',
          height: '100%',
          r: 10
        },
        style: {
          fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
          stroke: isDarkTheme ? '#333' : '#ccc',
          lineWidth: 0,
          cursor: 'default'
        }
      },
      {
        type: 'text',
        left: 10,
        top: 45,
        z: 101,
        style: {
          cursor: 'text',  // Sets text selection cursor
          userSelect: 'text',  // Enables text selection
          pointerEvents: 'auto',  // Ensures text interactions are enabled
          textStyle: {
            cursor: 'text',  // Reinforces text cursor on the actual text content
          },
          text: `{asset|$${formatNumber(sNet[0])} AUD} {pnl|${(() => {
            const pnl = sPnl[0];
            const prefix = pnl > 0 ? '▲' : (pnl < 0 ? '▼' : '●');
            return `${prefix} ${pnl.toFixed(2)}%`;
          })()}}`,
          textAlign: 'center',
          rich: {
            asset: {
              fontSize: 40,
              fontWeight: 'bold',
              fill: isDarkTheme ? 'white' : 'black',
            },
            pnl: {
              fontSize: 20,
              padding: [7, 0, 0, 10],
              fill: (() => {
                const pnl = sPnl[0];
                return pnl > 0 ? themeColors[1] : (pnl < 0 ? themeColors[2] : themeColors[0]);
              })()
            }
          }
        }
      }
    ]
  },
  series: context.panel.data.series.map((s, index) => ({
    cursor: 'default',
    tooltip: {
      show: false
    }
  })),
  media: [
    {
      // For larger screens (>=690px)
      query: { minWidth: 690 },
      option: {
        grid: {
          top: '40%'  // More space for larger screens with bigger text
        },
        graphic: {
          elements: [
            {
              // Rectangle config remains the same
              type: 'rect',
              left: 'center',
              top: 10,
              z: 100,
              shape: { width: '100%', height: '100%', r: 10 },
              style: {
                fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
                stroke: isDarkTheme ? '#333' : '#ccc',
                lineWidth: 0,
              }
            },
            {
              type: 'text',
              left: 10,
              top: 45,
              z: 101,
              style: {
                cursor: 'text',  // Sets text selection cursor
                userSelect: 'text',  // Enables text selection
                pointerEvents: 'auto',  // Ensures text interactions are enabled
                textStyle: {
                  cursor: 'text',  // Reinforces text cursor on the actual text content
                },
                text: `{asset|$${formatNumber(sNet[0])} AUD} {pnl|${(() => {
                  const pnl = sPnl[0];
                  const prefix = pnl > 0 ? '▲' : (pnl < 0 ? '▼' : '●');
                  return `${prefix} ${pnl.toFixed(2)}%`;
                })()}}`,
                textAlign: 'center',
                rich: {
                  asset: {
                    fontSize: 60,
                    fontWeight: 'bold',
                    fill: isDarkTheme ? 'white' : 'black',
                  },
                  pnl: {
                    fontSize: 30,
                    padding: [7, 0, 0, 10],
                    fill: (() => {
                      const pnl = sPnl[0];
                      return pnl > 0 ? themeColors[1] : (pnl < 0 ? themeColors[2] : themeColors[0]);
                    })()
                  }
                }
              }
            }
          ]
        }
      }
    },
    {
      // For smaller screens (<690px)
      query: { maxWidth: 691 },
      option: {
        grid: {
          top: '50%'  // Less space needed for smaller screens
        },
        graphic: {
          elements: [
            {
              // Rectangle config remains the same
              type: 'rect',
              left: 'center',
              top: 10,
              z: 100,
              shape: { width: '100%', height: '100%', r: 10 },
              style: {
                fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
                stroke: isDarkTheme ? '#333' : '#ccc',
                lineWidth: 0,
              }
            },
            {
              type: 'text',
              left: 10,
              top: 45,
              z: 101,
              style: {
                cursor: 'text',  // Sets text selection cursor
                userSelect: 'text',  // Enables text selection
                pointerEvents: 'auto',  // Ensures text interactions are enabled
                textStyle: {
                  cursor: 'text',  // Reinforces text cursor on the actual text content
                },
                text: `{asset|$${formatNumber(sNet[0])} AUD} {pnl|${(() => {
                  const pnl = sPnl[0];
                  const prefix = pnl > 0 ? '▲' : (pnl < 0 ? '▼' : '●');
                  return `${prefix} ${pnl.toFixed(2)}%`;
                })()}}`,
                textAlign: 'center',
                rich: {
                  asset: {
                    fontSize: 40,
                    fontWeight: 'bold',
                    fill: isDarkTheme ? 'white' : 'black',
                  },
                  pnl: {
                    fontSize: 20,
                    padding: [7, 0, 0, 10],
                    fill: (() => {
                      const pnl = sPnl[0];
                      return pnl > 0 ? themeColors[1] : (pnl < 0 ? themeColors[2] : themeColors[0]);
                    })()
                  }
                }
              }
            }
          ]
        }
      }
    }
  ],
  series,
};


