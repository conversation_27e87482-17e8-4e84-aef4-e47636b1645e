import pandas as pd

# Create a dictionary for the main sections
dashboard_data = {
    'Section': [
        'Dashboard',
        'Heatmap',
        'Portfolio Details',
        'Portfolio List',
        'Portfolio Card',
        'Trading Account',
        'Rebalance Action'
    ],
    'Requirements': [
        [
            'Total Net Asset: Line chart visualization',
            'Mini chart Net Assets: Add line chart over bar chart',
            'Portfolio DB Widget: In development',
            'Top Account Value: Add Account Name column'
        ],
        [
            'Data DB: Quarter/Month filter (Currently Today and Total)',
            'Default YTD with day-to-day option',
            'Export functionality (PDF/Excel/Image)',
            'Email <NAME_EMAIL>'
        ],
        [
            'Convert "View" to button',
            'Add Step arrows (1/2/3)',
            'Rename to Portfolio Strategy',
            'Set allocation based on total investment',
            'Transaction Fee as dropdown',
            'Ticker format: ticker.exchange'
        ],
        [
            'Tooltip for short description',
            'Author info with show/hide option',
            'Character limit for portfolio name'
        ],
        [
            'Replace Expected return with 30-day and YTD P&L',
            'Risk Score using star rating (Sharpe Ratio)',
            'Replace New Buy Point with Holdings count'
        ],
        [
            'Historical adjustment: Add creator/approver/timestamp'
        ],
        [
            'Add account ID column',
            'Symbol format: Symbol.exchange + company name',
            'Price: Default Market to Limit',
            'Add Destination column (ASX/ASX BEST/TR)',
            'Duration options: Day/Good to Cancel/Good till date',
            'Export to CSV functionality',
            'Review/Confirm screen with Back button'
        ]
    ]
}

# Convert to DataFrame
df = pd.DataFrame(dashboard_data)

# Export to Excel
df.to_excel('dashboard_requirements.xlsx', index=False)
