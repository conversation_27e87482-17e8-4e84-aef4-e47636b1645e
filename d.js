const hasData = context.panel.data.series[0].fields.length > 0;

if (!hasData) {
  // Return a configuration that displays a custom error message
  return {
    title: {
      text: 'No data available',
      left: 'center',
      top: 'center',
      textStyle: {
        color: 'grey',
        fontSize: 20,
      },
    },
    backgroundColor: 'transparent',
  };
}

// Determine the current theme
const isDarkTheme = context.grafana.theme.isDark === true;
// Define colors for light and dark themes
// const lightThemeColors = ['#eab308', '#3ea32a', '#fd2121', '#3366ff', '#ebf0ff'];
// const darkThemeColors = ['#eab308', '#4fd036', '#fd4d4d', '#3366ff', '#152B6B'];

const lightThemeColors = ['#888888', '#3ea32a', '#fd2121', '#3366ff', '#ebf0ff'];
const darkThemeColors = ['#888888', '#4fd036', '#fd4d4d', '#3366ff', '#152B6B'];
// Select the appropriate color set based on the theme
const themeColors = isDarkTheme ? darkThemeColors : lightThemeColors;

const formatNumber = (num) => {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(2) + 'B';
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(2) + 'M';
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(2) + 'K';
  } else {
    return num.toFixed(2);
  }
};

const formatNumberWithCommas = (num) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num);
};

// Custom function to round up to the nearest integer
const roundUp = (num) => Math.ceil(num);

// Calculate the minimum and maximum values of the data
const minValue = Math.min(...context.panel.data.series.flatMap(s => s.fields.find(f => f.name === 'net_asset').values));
const maxValue = Math.max(...context.panel.data.series.flatMap(s => s.fields.find(f => f.name === 'net_asset').values));

// Extend the y-axis range by 10% both above the maximum value and below the minimum value
const yAxisMin = minValue > 0 ? roundUp(minValue * 0.9) : roundUp(minValue * 1.1);
const yAxisMax = roundUp(maxValue * 1.1);

const sNet = context.panel.data.series.flatMap((s) => s.fields.find((f) => f.name === 'net_asset').values);
const sPnl = context.panel.data.series.flatMap((s) => s.fields.find((f) => f.name === 'today_account_pnl').values).map(value => value * 100);
const sTime = context.panel.data.series.flatMap((s) => s.fields.find((f) => f.name === 'updated_at').values);

// Calculate current PNL value (current net - closest previous net)
const currentNet = sNet[sNet.length - 1];
const previousNet = sNet.length > 1 ? sNet[sNet.length - 2] : null;
const pnlValue = previousNet !== null ? currentNet - previousNet : 0;
const pnlPercentage = previousNet !== null ? (pnlValue / previousNet) * 100 : 0;

// Calculate monthly PNL (today's net - last month's net)
const currentDate = new Date(sTime[sTime.length - 1]);
const oneMonthAgo = new Date(currentDate);
oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

// Find the closest data point to one month ago
let lastMonthIndex = -1;
let minTimeDiff = Infinity;

for (let i = 0; i < sTime.length; i++) {
  const dataPointDate = new Date(sTime[i]);
  const timeDiff = Math.abs(dataPointDate - oneMonthAgo);

  if (timeDiff < minTimeDiff) {
    minTimeDiff = timeDiff;
    lastMonthIndex = i;
  }
}

// Calculate monthly PNL if we have data from a month ago
const monthlyPnlValue = lastMonthIndex !== -1 ? currentNet - sNet[lastMonthIndex] : null;
const monthlyPnlPercentage = lastMonthIndex !== -1 ? (monthlyPnlValue / sNet[lastMonthIndex]) * 100 : null;

const dataPnl = sPnl.map((pnl) => ({
  value: Math.abs(pnl) < 0.0099 ? 0 : pnl
}));

const dataNet = sNet.map((net, i) => ({
  value: net,
  time: sTime[i],
}));

const series = [
  {
    name: 'Net Asset Profile',
    type: 'line',
    showSymbol: false,
    areaStyle: {
      opacity: 0.3,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: themeColors[3]
        }, {
          offset: 1,
          color: '#ffffff'
        }]
      }
    },
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: themeColors[3]
        }, {
          offset: 1,
          color: themeColors[4]
        }]
      }
    },
    smooth: true,
    data: dataNet,
    silent: true,
    cursor: 'default',
    emphasis: {
      disabled: true
    },
    tooltip: {
      show: false
    }
  },
]

/**
 * Enable Data Zoom by default
 */
setTimeout(() => context.panel.chart.dispatchAction({
  type: 'takeGlobalCursor',
  key: 'dataZoomSelect',
  dataZoomSelectActive: true,
}), 500);

/**
 * Update Time Range on Zoom
 */
context.panel.chart.on('datazoom', function (params) {
  const startValue = params.batch[0]?.startValue;
  const endValue = params.batch[0]?.endValue;
  locationService.partial({ from: startValue, to: endValue });
});

// Helper function to get color based on value
const getColorForValue = (value) => {
  if (value > 0) return themeColors[1]; // positive
  if (value < 0) return themeColors[2]; // negative
  return themeColors[0]; // neutral
};

// Helper function to get prefix based on value
const getPrefixForValue = (value) => {
  if (value > 0) return '▲';
  if (value < 0) return '▼';
  return '●';
};

return {
  backgroundColor: 'transparent',
  tooltip: {
    // trigger: 'axis',
  },
  legend: {
    show: false,
    left: '0',
    bottom: '0',
    data: context.panel.data.series.map((s) => s.refId),
    textStyle: {
      color: 'rgba(128, 128, 128, .9)',
    },
  },
  xAxis: {
    type: 'category',
    show: false,
  },
  yAxis: {
    show: false,
    type: 'value',
    min: yAxisMin,
    max: yAxisMax,
  },
  grid: {
    left: '60%',  // Move the chart to the right side
    right: '5%',
    top: '10%',   // Reduce top margin to make chart taller
    bottom: '10%',
    width: '35%', // Make the chart width smaller
    height: '35%',
    containLabel: false
  },
  graphic: {
    elements: [
      // Left panel - Net Asset, Daily PNL, and Monthly PNL
      {
        type: 'rect',
        left: '5%',
        top: 10,
        z: 100,
        shape: {
          width: '40%',  // Reduce rectangle width to 40% to keep it more to the left
          height: '100%',
          r: 10
        },
        style: {
          fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
          stroke: isDarkTheme ? '#333' : '#ccc',
          lineWidth: 0,
          cursor: 'default'
        }
      },
      // Net Asset Value
      {
        type: 'text',
        left: '5%',  // Position at left side
        top: '20%',  // Position near the top
        z: 101,
        style: {
          cursor: 'text',
          userSelect: 'text',
          pointerEvents: 'auto',
          position: 'left',  // Position the text at the left
          textAlign: 'left', // Left-align the text content
          textVerticalAlign: 'middle', // Vertically center the text
          text: `{asset|$${formatNumberWithCommas(sNet[sNet.length - 1])}} AUD`,
          rich: {
            asset: {
              fontSize: 40,
              fontWeight: 'bold',
              fill: isDarkTheme ? 'white' : 'black',
            },
            pnl: {
              fontSize: 20,
              padding: [7, 0, 0, 10],
              fill: getColorForValue(sPnl[sPnl.length - 1])
            }
          }
        }
      },

      // Daily PNL
      {
        type: 'text',
        left: '5%',  // Position at left side
        top: '40%',  // Position in the middle
        z: 101,
        style: {
          cursor: 'text',
          userSelect: 'text',
          pointerEvents: 'auto',
          position: 'left',  // Position the text at the left
          textAlign: 'left', // Left-align the text content
          textVerticalAlign: 'middle', // Vertically center the text
          text: `{title|Daily PNL}\n{value|$${formatNumberWithCommas(pnlValue)} AUD} {percent|${(() => {
            const prefix = getPrefixForValue(pnlPercentage);
            return `${prefix} ${Math.abs(pnlPercentage).toFixed(2)}%`;
          })()}}`,
          rich: {
            title: {
              fontSize: 14,
              fontWeight: 'bold',
              fill: isDarkTheme ? '#aaa' : '#666',
              padding: [0, 0, 0, 0]
            },
            value: {
              fontSize: 14,
              fontWeight: 'bold',
              fill: isDarkTheme ? 'white' : 'black',
              padding: [5, 0, 0, 0]
            },
            percent: {
              fontSize: 14,
              fill: getColorForValue(pnlPercentage),
              padding: [5, 0, 0, 10]
            }
          }
        }
      },

      // Monthly PNL (only show if we have data)
      ...(monthlyPnlValue !== null ? [
        {
          type: 'text',
          left: '5%',  // Position at left side
          top: '60%',  // Position at the bottom
          z: 101,
          style: {
            cursor: 'text',
            userSelect: 'text',
            pointerEvents: 'auto',
            position: 'left',  // Position the text at the left
            textAlign: 'left', // Left-align the text content
            textVerticalAlign: 'middle', // Vertically center the text
            text: `{title|Monthly PNL}\n{value|$${formatNumberWithCommas(monthlyPnlValue)} AUD} {percent|${(() => {
              const prefix = getPrefixForValue(monthlyPnlPercentage);
              return `${prefix} ${Math.abs(monthlyPnlPercentage).toFixed(2)}%`;
            })()}}`,
            rich: {
              title: {
                fontSize: 14,
                fontWeight: 'bold',
                fill: isDarkTheme ? '#aaa' : '#666',
                padding: [0, 0, 0, 0]
              },
              value: {
                fontSize: 14,
                fontWeight: 'bold',
                fill: isDarkTheme ? 'white' : 'black',
                padding: [5, 0, 0, 0]
              },
              percent: {
                fontSize: 14,
                fill: getColorForValue(monthlyPnlPercentage),
                padding: [5, 0, 0, 10]
              }
            }
          }
        }
      ] : []),

      // We don't need a right panel rectangle since the chart is on the right side
    ]
  },
  media: [
    // {
    //   // For larger screens (>=690px)
    //   query: { minWidth: 690 },
    //   option: {
    //     grid: {
    //       left: '60%',  // Move the chart to the right side
    //       right: '5%',
    //       top: '30%',   // Reduce top margin to make chart taller
    //       bottom: '30%',
    //       width: '35%', // Make the chart width smaller
    //     },
    //     graphic: {
    //       elements: [
    //         // Left panel - Net Asset, Daily PNL, and Monthly PNL
    //         {
    //           type: 'rect',
    //           left: '5%',
    //           top: 10,
    //           z: 100,
    //           shape: {
    //             width: '40%',  // Reduce rectangle width to 40% to keep it more to the left
    //             height: '100%',
    //             r: 10
    //           },
    //           style: {
    //             fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
    //             stroke: isDarkTheme ? '#333' : '#ccc',
    //             lineWidth: 0,
    //           }
    //         },
    //         // Net Asset Value
    //         {
    //           type: 'text',
    //           left: 20,
    //           top: 10,
    //           z: 101,
    //           style: {
    //             cursor: 'text',
    //             userSelect: 'text',
    //             pointerEvents: 'auto',
    //             textStyle: {
    //               cursor: 'text',
    //             },
    //             // text: `{asset|${formatNumberWithCommas(sNet[sNet.length - 1])} AUD} {pnl|${(() => {
    //             //   const pnl = sPnl[sPnl.length - 1];
    //             //   const prefix = getPrefixForValue(pnl);
    //             //   return `${prefix} ${Math.abs(pnl).toFixed(2)}%`;
    //             // })()}}`,
    //             text: `{asset|${formatNumberWithCommas(sNet[sNet.length - 1])} AUD}`,
    //             textAlign: 'left',
    //             rich: {
    //               asset: {
    //                 fontSize: 60,
    //                 fontWeight: 'bold',
    //                 fill: isDarkTheme ? 'white' : 'black',
    //               },
    //               pnl: {
    //                 fontSize: 30,
    //                 padding: [7, 0, 0, 10],
    //                 fill: getColorForValue(sPnl[sPnl.length - 1]),
    //                 padding: [0, 0, 0, 10]
    //               }
    //             }
    //           }
    //         },

    //         // Daily PNL
    //         {
    //           type: 'text',
    //           left: 20,
    //           top: 100,
    //           z: 101,
    //           style: {
    //             cursor: 'text',
    //             userSelect: 'text',
    //             pointerEvents: 'auto',
    //             textStyle: {
    //               cursor: 'text',
    //             },
    //             text: `{title|Daily PNL} {value|${formatNumberWithCommas(pnlValue)} AUD} {percent|${(() => {
    //               const prefix = getPrefixForValue(pnlPercentage);
    //               return `${prefix} ${Math.abs(pnlPercentage).toFixed(2)}%`;
    //             })()}}`,
    //             textAlign: 'left',
    //             rich: {
    //               title: {
    //                 fontSize: 18,
    //                 fontWeight: 'bold',
    //                 fill: isDarkTheme ? '#aaa' : '#666',
    //                 padding: [0, 0, 0, 0]
    //               },
    //               value: {
    //                 fontSize: 18,
    //                 fontWeight: 'bold',
    //                 fill: isDarkTheme ? 'white' : 'black',
    //                 padding: [0, 0, 0, 10]
    //               },
    //               percent: {
    //                 fontSize: 18,
    //                 fill: getColorForValue(pnlPercentage),
    //                 padding: [0, 0, 0, 10]
    //               }
    //             }
    //           }
    //         },

    //         // Monthly PNL (only show if we have data)
    //         ...(monthlyPnlValue !== null ? [
    //           {
    //             type: 'text',
    //             left: 20,
    //             top: 130,
    //             z: 101,
    //             style: {
    //               cursor: 'text',
    //               userSelect: 'text',
    //               pointerEvents: 'auto',
    //               textStyle: {
    //                 cursor: 'text',
    //               },
    //               text: `{title|Monthly PNL} {value|${formatNumberWithCommas(monthlyPnlValue)} AUD} {percent|${(() => {
    //                 const prefix = getPrefixForValue(monthlyPnlPercentage);
    //                 return `${prefix} ${Math.abs(monthlyPnlPercentage).toFixed(2)}%`;
    //               })()}}`,
    //               textAlign: 'left',
    //               rich: {
    //                 title: {
    //                   fontSize: 18,
    //                   fontWeight: 'bold',
    //                   fill: isDarkTheme ? '#aaa' : '#666',
    //                   padding: [0, 0, 0, 0]
    //                 },
    //                 value: {
    //                   fontSize: 18,
    //                   fontWeight: 'bold',
    //                   fill: isDarkTheme ? 'white' : 'black',
    //                   padding: [0, 0, 0, 10]
    //                 },
    //                 percent: {
    //                   fontSize: 18,
    //                   fill: getColorForValue(monthlyPnlPercentage),
    //                   padding: [0, 0, 0, 10]
    //                 }
    //               }
    //             }
    //           }
    //         ] : []),

    //         // We don't need a right panel rectangle since the chart is on the right side
    //       ]
    //     }
    //   }
    // },
    // {
    //   // For smaller screens (<690px)
    //   query: { maxWidth: 691 },
    //   option: {
    //     grid: {
    //       left: '60%',  // Move the chart to the right side
    //       right: '5%',
    //       top: '10%',   // Reduce top margin to make chart taller
    //       bottom: '10%',
    //       width: '35%', // Make the chart width smaller
    //     },
    //     graphic: {
    //       elements: [
    //         // Left panel - Net Asset, Daily PNL, and Monthly PNL
    //         {
    //           type: 'rect',
    //           left: '5%',
    //           top: 10,
    //           z: 100,
    //           shape: {
    //             width: '40%',  // Reduce rectangle width to 40% to keep it more to the left
    //             height: '100%',
    //             r: 10
    //           },
    //           style: {
    //             fill: isDarkTheme ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)',
    //             stroke: isDarkTheme ? '#333' : '#ccc',
    //             lineWidth: 0,
    //           }
    //         },
    //         // Net Asset Value
    //         {
    //           type: 'text',
    //           left: 20,
    //           top: 10,
    //           z: 101,
    //           style: {
    //             cursor: 'text',
    //             userSelect: 'text',
    //             pointerEvents: 'auto',
    //             textStyle: {
    //               cursor: 'text',
    //             },
    //             // text: `{asset|${formatNumberWithCommas(sNet[sNet.length - 1])} AUD} {pnl|${(() => {
    //             //   const pnl = sPnl[sPnl.length - 1];
    //             //   const prefix = getPrefixForValue(pnl);
    //             //   return `${prefix} ${Math.abs(pnl).toFixed(2)}%`;
    //             // })()}}`,
    //             text: `{asset|${formatNumberWithCommas(sNet[sNet.length - 1])} AUD}`,
    //             textAlign: 'left',
    //             rich: {
    //               asset: {
    //                 fontSize: 30,
    //                 fontWeight: 'bold',
    //                 fill: isDarkTheme ? 'white' : 'black',
    //               },
    //               pnl: {
    //                 fontSize: 16,
    //                 padding: [7, 0, 0, 10],
    //                 fill: getColorForValue(sPnl[sPnl.length - 1])
    //               }
    //             }
    //           }
    //         },

    //         // Daily PNL
    //         {
    //           type: 'text',
    //           left: 20,
    //           top: 60,
    //           z: 101,
    //           style: {
    //             cursor: 'text',
    //             userSelect: 'text',
    //             pointerEvents: 'auto',
    //             textStyle: {
    //               cursor: 'text',
    //             },
    //             text: `{title|Daily PNL} {value|${formatNumberWithCommas(pnlValue)} AUD} {percent|${(() => {
    //               const prefix = getPrefixForValue(pnlPercentage);
    //               return `${prefix} ${Math.abs(pnlPercentage).toFixed(2)}%`;
    //             })()}}`,
    //             textAlign: 'left',
    //             rich: {
    //               title: {
    //                 fontSize: 12,
    //                 fontWeight: 'bold',
    //                 fill: isDarkTheme ? '#aaa' : '#666',
    //                 padding: [0, 0, 0, 0]
    //               },
    //               value: {
    //                 fontSize: 12,
    //                 fontWeight: 'bold',
    //                 fill: isDarkTheme ? 'white' : 'black',
    //                 padding: [0, 0, 0, 10]
    //               },
    //               percent: {
    //                 fontSize: 12,
    //                 fill: getColorForValue(pnlPercentage),
    //                 padding: [0, 0, 0, 10]
    //               }
    //             }
    //           }
    //         },

    //         // Monthly PNL (only show if we have data)
    //         ...(monthlyPnlValue !== null ? [
    //           {
    //             type: 'text',
    //             left: 20,
    //             top: 80,
    //             z: 101,
    //             style: {
    //               cursor: 'text',
    //               userSelect: 'text',
    //               pointerEvents: 'auto',
    //               textStyle: {
    //                 cursor: 'text',
    //               },
    //               text: `{title|Monthly PNL} {value|${formatNumberWithCommas(monthlyPnlValue)} AUD} {percent|${(() => {
    //                 const prefix = getPrefixForValue(monthlyPnlPercentage);
    //                 return `${prefix} ${Math.abs(monthlyPnlPercentage).toFixed(2)}%`;
    //               })()}}`,
    //               textAlign: 'left',
    //               rich: {
    //                 title: {
    //                   fontSize: 12,
    //                   fontWeight: 'bold',
    //                   fill: isDarkTheme ? '#aaa' : '#666',
    //                   padding: [0, 0, 0, 0]
    //                 },
    //                 value: {
    //                   fontSize: 12,
    //                   fontWeight: 'bold',
    //                   fill: isDarkTheme ? 'white' : 'black',
    //                   padding: [0, 0, 0, 10]
    //                 },
    //                 percent: {
    //                   fontSize: 12,
    //                   fill: getColorForValue(monthlyPnlPercentage),
    //                   padding: [0, 0, 0, 10]
    //                 }
    //               }
    //             }
    //           }
    //         ] : []),

    //         // We don't need a right panel rectangle since the chart is on the right side
    //       ]
    //     }
    //   }
    // }
  ],
  series,
};