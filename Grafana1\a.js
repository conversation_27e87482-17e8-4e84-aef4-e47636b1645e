//Content

<div class="panel-container">
  <div class="title">Cash Balance</div>
  <div class="net-diff-container">
    <div class="net-value">
      {{data.0.cash}} AUD
    </div>
    <div class="diff-value"
      data-trend="{{#if (and (gt data.0.cash_percentage -0.0001) (lt data.0.cash_percentage 0.0001))}}neutral{{else if (gt data.0.cash_percentage 0)}}positive{{else if (lt data.0.cash_percentage 0)}}negative{{else}}neutral{{/if}}">
      <span class="trend-indicator"></span>
      <span class="trend-value">{{toFixed data.0.cash_percentage 2}}%</span>
    </div>
  </div>
</div>


//Styles

.panel-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start; /* Left align horizontally */
  text-align: left; /* Left align text */
  overflow: hidden;
  width: 100%;
  padding: 10px;
  position: relative;
}

.title {
  font-size: 1.5em;
  font-weight: bold;
  color: #888888; /* Grey color */
  margin-bottom: 15px;
  text-align: left;
}

.net-diff-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Left align vertically */
  justify-content: flex-start; /* Left align horizontally */
  width: 100%;
}

.net-value {
  font-size: 2.5em;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 10px;
  text-align: left;
}

.diff-value {
  font-size: 1.5em;
  line-height: 1;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Style based on trend data attribute */
.diff-value[data-trend="positive"] {
  color: #79EA62;
}

.diff-value[data-trend="negative"] {
  color: #FD4D4D;
}

.diff-value[data-trend="neutral"] {
  color: #888888;
}

/* Trend indicators */
.trend-indicator {
  display: inline-block;
  margin-right: 4px;
}

.diff-value[data-trend="positive"] .trend-indicator::before {
  content: "▲";
}

.diff-value[data-trend="negative"] .trend-indicator::before {
  content: "▼";
}

.diff-value[data-trend="neutral"] .trend-indicator::before {
  content: "●";
}